<script lang="ts">
import { ref, onMounted } from 'vue'
import { fabYoutube } from '@quasar/extras/fontawesome-v6';
import { menuSections } from './menus';
import { useRouter } from 'vue-router';
import { QSpinnerFacebook, useQuasar } from 'quasar';
import { checkToken, getToken } from 'src/helpers/myfunc';
import { authStore } from 'src/stores/auth/authStore';
import { adminStore } from 'src/stores/core/adminStore';
import { storeToRefs } from 'pinia';
import { computed } from 'vue';
import { productStore } from 'src/stores/core/productStore';
import logo from 'src/assets/logo.jpg';
import MenuSection from './MenuSection.vue';

export default {
  name: 'MyLayout',
  components: {
    MenuSection,
  },

  setup() {
    const $q = useQuasar();
    const leftDrawerOpen = ref(false)
    const search = ref('');
    const router = useRouter();
    const isLoading = ref(false);
    const { logout } = authStore();

    const scrollStyles = {
      thumbStyle: {
        right: '1px',
        borderRadius: '10px',
        backgroundColor: '#eee',
        opacity: '0.5',
        width: '1px',
      },
      barStyle: {
        right: '1px',
        borderRadius: '10px',
        backgroundColor: '#eee',
        opacity: '0.5',
        width: '1px',
      },
    };

    const store = adminStore();
    const { countries, cities, quarters, roles, agencies } = storeToRefs(adminStore());
    const { categories } = storeToRefs(productStore());
    const prodStore = productStore();
    const { getCountries, getCities, getQuarters, getRoles, getAgencies } = store;

    const isDataLoaded = computed(() =>
      countries.value.length > 0 &&
      cities.value.length > 0 &&
      quarters.value.length > 0 &&
      roles.value.length > 0 &&
      agencies.value.length > 0 &&
      categories.value.length > 0
    );


    function toggleLeftDrawer() {
      leftDrawerOpen.value = !leftDrawerOpen.value
    }

    const showLoading = () => {
      if (isLoading.value) {
        $q.loading.show({
          spinner: QSpinnerFacebook,
          spinnerColor: 'yellow',

          spinnerSize: 140,
          backgroundColor: 'cyan-2',
          message: 'Chargement des données en cours, patientez quelques instants svp ...!',
          messageColor: 'black'
        });
      }
    };

    const hideLoading = () => {
      setTimeout(() => {
        $q.loading.hide();
      }, 3000);
    };

    const onLogout = async () => {
      try {
        console.log("user on disconnect");
        const res = await logout();
        if (res.success) {
          $q.notify({
            type: "positive",
            position: "top-right",
            message: res.message,
            timeout: 2500,
          });
          setTimeout(() => {
            router.push({name: "login"});
          }, 2500);
        } else {
          $q.notify({
            type: "negative",
            position: "top-right",
            message: res.message,
            timeout: 2500,
          });
        }
      } catch (error) {
        $q.notify({
          type: "positive",
          position: "top-right",
          message: "Echec, une erreur interne est survenue",
          timeout: 2500,
        });
      }
    };

    const initData = async () => {
      if (!isDataLoaded.value) {  // Ne chargez que si les données sont vides
        isLoading.value = true;
        showLoading();
        try {
          await Promise.all([
            getCountries(),
            getCities(),
            getQuarters(),
            getRoles(),
            getAgencies({ page: 1, limit: 50 }),
            prodStore.getCategories()
          ]);
        } catch (error) {
          console.error("Error initializing data:", error);
        } finally {
          isLoading.value = false;
          hideLoading();
        }
      } else {
        console.log("Data is already loaded in store");
      }
    };


    onMounted(async () => {
      window.document.title = "Dashboard";

      const is_auth = checkToken();
      if (!is_auth) {
        router.push({name: 'login'});
      } else {
        // console.log("init data");
        await initData();
      }
      setInterval(() => {
        const token = getToken();
        console.log("token",token);

        if (token == null) {
          router.push({name: 'login'});
        }
      }, 1000);
    });

    return {
      fabYoutube, leftDrawerOpen, search, menuSections, logo,
      toggleLeftDrawer, onLogout, scrollStyles
    };
  }
}
</script>

<template>
  <q-layout view="lHr LpR fFf" class="bg-grey-1">
    <q-header class="bg-white text-grey-8 q-py-xs" height-hint="58">
      <q-toolbar>
        <q-btn flat dense round @click="toggleLeftDrawer" aria-label="Menu" icon="menu" />

        <q-btn flat no-caps no-wrap class="q-ml-xs" v-if="$q.screen.gt.xs">
          <!-- <q-icon name="credit_card" color="red" size="28px" /> -->
          <q-toolbar-title shrink class="text-cyan-4">
            Dashboard
          </q-toolbar-title>
        </q-btn>

        <q-space />

        <div class="q-gutter-sm row items-center no-wrap">

          <q-btn round dense flat color="grey-8" icon="apps" v-if="$q.screen.gt.sm">
            <q-tooltip>Apps</q-tooltip>
          </q-btn>

          <q-btn round dense flat color="grey-8" icon="notifications">
            <q-badge color="red" text-color="white" floating>
              0
            </q-badge>
            <q-tooltip>Notifications</q-tooltip>
          </q-btn>
          <q-btn-dropdown color="primary" flat>
            <template v-slot:label>
              <q-avatar size="30px">
                <q-icon name="account_circle" />
              </q-avatar>
            </template>
            <div class="row no-wrap q-pa-md">
              <div class="column">
                <div class="text-h6 q-mb-md">Settings</div>
                <q-list>
              <q-item clickable v-close-popup v-ripple>
                <q-item-section>
                  <q-item-label>Profile</q-item-label>
                </q-item-section>
              </q-item>
              <q-item clickable v-close-popup v-ripple>
                <q-item-section>
                  <q-item-label>Paramètre</q-item-label>
                </q-item-section>
              </q-item>

            </q-list>
              </div>

              <q-separator vertical inset class="q-mx-lg" />

              <div class="column items-center">
                <q-avatar size="72px">
                  <img src="https://cdn.quasar.dev/img/boy-avatar.png">
                </q-avatar>

                <div class="text-subtitle1 q-mt-md q-mb-xs">John Doe</div>

                <q-btn color="primary" label="Deconnexion" push size="sm" v-close-popup  @click="onLogout" />
              </div>
            </div>

          </q-btn-dropdown>
        </div>
      </q-toolbar>
    </q-header>

    <q-drawer v-model="leftDrawerOpen" show-if-above bordered class=" sidebar text-dark " :width="275"
      :breakpoint="600"  >
      <div class="absolute-top img-header ">
        <div style="display: flex; align-items: center;">
          <q-avatar size="50px" style="margin-right: 8px;">
            <q-img :src="logo" cover />
          </q-avatar>
          <div>
            <span class="text-h6 text-primary">Iziway</span>
          </div>
        </div>
      </div>
      <q-scroll-area class="fit scrollarea" :thumb-style="scrollStyles.thumbStyle" :bar-style="scrollStyles.barStyle">
        <q-list padding class="q-px-sm" style="padding-bottom: 80px !important;">
          <template v-for="(section, index) in menuSections" :key="index">
            <MenuSection :title="section.title" :links="section.links" />
          </template>
        </q-list>
      </q-scroll-area>
    </q-drawer>

    <q-page-container class="main">
      <router-view />
    </q-page-container>
  </q-layout>
</template>

<style>
.app-header {
  background-color: white;
}

.main {
  background-color: #F3F4F6;
  /* overflow: hidden; */
}

.scrollarea {
  height: calc(100vh - 170px);
  margin-top: 70px;
  border-right: none;
}

.sidebar {
  background: #fcfcfa;
  /* background: linear-gradient(90deg, rgba(255, 255, 255, 1) 10%, rgba(102, 204, 153, 1) 90%); */
  height: 100%;
  display: flex;
  flex-direction: column;
  color: white;
  border: none;
  position: relative;
}

.absolute-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: inherit;
  z-index: 2;
}

.absolute-top {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background: inherit;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.img-header {
  width: 96%;
  height: 70px;
  display: flex;
  margin-left: 10px;
  align-items: start;
  justify-content: center;
  flex-direction: column;
}

::-webkit-scrollbar {
  width: 7px;
  height: 7px;
}

::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  border-radius: 10px;
  opacity: 0.7;
  height: 7px;
  width: 7px;
  background: rgb(182, 157, 157);
}

</style>
